import matplotlib.pyplot as plt
import numpy as np
from scipy import interpolate
import matplotlib.font_manager as fm

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class GrowthChartHK:
    def __init__(self):
        # 原有的身高和体重数据保持不变...
        
        # 男孩头围数据 (0-2岁，按月份)
        self.boys_head_0_2 = {
            'ages': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 18, 21, 24],  # 月份
            'p3': [32.0, 34.5, 36.0, 37.2, 38.2, 39.0, 39.6, 40.2, 40.6, 41.0, 41.3, 41.6, 41.9, 42.8, 43.5, 44.1, 44.6],
            'p10': [33.2, 35.7, 37.2, 38.4, 39.4, 40.2, 40.8, 41.4, 41.8, 42.2, 42.5, 42.8, 43.1, 44.0, 44.7, 45.3, 45.8],
            'p25': [34.5, 37.0, 38.5, 39.7, 40.7, 41.5, 42.1, 42.7, 43.1, 43.5, 43.8, 44.1, 44.4, 45.3, 46.0, 46.6, 47.1],
            'p50': [35.8, 38.4, 39.9, 41.1, 42.1, 42.9, 43.5, 44.1, 44.5, 44.9, 45.2, 45.5, 45.8, 46.7, 47.4, 48.0, 48.5],
            'p75': [37.2, 39.8, 41.3, 42.5, 43.5, 44.3, 44.9, 45.5, 45.9, 46.3, 46.6, 46.9, 47.2, 48.1, 48.8, 49.4, 49.9],
            'p90': [38.5, 41.1, 42.6, 43.8, 44.8, 45.6, 46.2, 46.8, 47.2, 47.6, 47.9, 48.2, 48.5, 49.4, 50.1, 50.7, 51.2],
            'p97': [40.0, 42.6, 44.1, 45.3, 46.3, 47.1, 47.7, 48.3, 48.7, 49.1, 49.4, 49.7, 50.0, 50.9, 51.6, 52.2, 52.7]
        }
        
        # 女孩头围数据 (0-2岁，按月份)
        self.girls_head_0_2 = {
            'ages': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 18, 21, 24],  # 月份
            'p3': [31.5, 33.8, 35.2, 36.3, 37.2, 37.9, 38.5, 39.0, 39.4, 39.7, 40.0, 40.3, 40.5, 41.3, 42.0, 42.5, 43.0],
            'p10': [32.7, 35.0, 36.4, 37.5, 38.4, 39.1, 39.7, 40.2, 40.6, 40.9, 41.2, 41.5, 41.7, 42.5, 43.2, 43.7, 44.2],
            'p25': [34.0, 36.3, 37.7, 38.8, 39.7, 40.4, 41.0, 41.5, 41.9, 42.2, 42.5, 42.8, 43.0, 43.8, 44.5, 45.0, 45.5],
            'p50': [35.4, 37.7, 39.1, 40.2, 41.1, 41.8, 42.4, 42.9, 43.3, 43.6, 43.9, 44.2, 44.4, 45.2, 45.9, 46.4, 46.9],
            'p75': [36.8, 39.1, 40.5, 41.6, 42.5, 43.2, 43.8, 44.3, 44.7, 45.0, 45.3, 45.6, 45.8, 46.6, 47.3, 47.8, 48.3],
            'p90': [38.1, 40.4, 41.8, 42.9, 43.8, 44.5, 45.1, 45.6, 46.0, 46.3, 46.6, 46.9, 47.1, 47.9, 48.6, 49.1, 49.6],
            'p97': [39.6, 41.9, 43.3, 44.4, 45.3, 46.0, 46.6, 47.1, 47.5, 47.8, 48.1, 48.4, 48.6, 49.4, 50.1, 50.6, 51.1]
        }
        
        # 男孩头围数据 (2-18岁)
        self.boys_head_2_18 = {
            'ages': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
            'p3': [44.6, 45.8, 46.8, 47.6, 48.2, 48.7, 49.1, 49.4, 49.7, 49.9, 50.1, 50.3, 50.5, 50.7, 50.9, 51.0, 51.1],
            'p10': [45.8, 47.0, 48.0, 48.8, 49.4, 49.9, 50.3, 50.6, 50.9, 51.1, 51.3, 51.5, 51.7, 51.9, 52.1, 52.2, 52.3],
            'p25': [47.1, 48.3, 49.3, 50.1, 50.7, 51.2, 51.6, 51.9, 52.2, 52.4, 52.6, 52.8, 53.0, 53.2, 53.4, 53.5, 53.6],
            'p50': [48.5, 49.7, 50.7, 51.5, 52.1, 52.6, 53.0, 53.3, 53.6, 53.8, 54.0, 54.2, 54.4, 54.6, 54.8, 54.9, 55.0],
            'p75': [49.9, 51.1, 52.1, 52.9, 53.5, 54.0, 54.4, 54.7, 55.0, 55.2, 55.4, 55.6, 55.8, 56.0, 56.2, 56.3, 56.4],
            'p90': [51.2, 52.4, 53.4, 54.2, 54.8, 55.3, 55.7, 56.0, 56.3, 56.5, 56.7, 56.9, 57.1, 57.3, 57.5, 57.6, 57.7],
            'p97': [52.7, 53.9, 54.9, 55.7, 56.3, 56.8, 57.2, 57.5, 57.8, 58.0, 58.2, 58.4, 58.6, 58.8, 59.0, 59.1, 59.2]
        }
        
        # 女孩头围数据 (2-18岁)
        self.girls_head_2_18 = {
            'ages': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
            'p3': [43.0, 44.1, 45.0, 45.7, 46.2, 46.6, 46.9, 47.1, 47.3, 47.5, 47.6, 47.7, 47.8, 47.9, 48.0, 48.0, 48.1],
            'p10': [44.2, 45.3, 46.2, 46.9, 47.4, 47.8, 48.1, 48.3, 48.5, 48.7, 48.8, 48.9, 49.0, 49.1, 49.2, 49.2, 49.3],
            'p25': [45.5, 46.6, 47.5, 48.2, 48.7, 49.1, 49.4, 49.6, 49.8, 50.0, 50.1, 50.2, 50.3, 50.4, 50.5, 50.5, 50.6],
            'p50': [46.9, 48.0, 48.9, 49.6, 50.1, 50.5, 50.8, 51.0, 51.2, 51.4, 51.5, 51.6, 51.7, 51.8, 51.9, 51.9, 52.0],
            'p75': [48.3, 49.4, 50.3, 51.0, 51.5, 51.9, 52.2, 52.4, 52.6, 52.8, 52.9, 53.0, 53.1, 53.2, 53.3, 53.3, 53.4],
            'p90': [49.6, 50.7, 51.6, 52.3, 52.8, 53.2, 53.5, 53.7, 53.9, 54.1, 54.2, 54.3, 54.4, 54.5, 54.6, 54.6, 54.7],
            'p97': [51.1, 52.2, 53.1, 53.8, 54.3, 54.7, 55.0, 55.2, 55.4, 55.6, 55.7, 55.8, 55.9, 56.0, 56.1, 56.1, 56.2]
        }
        
        # 原有的身高和体重数据...
        self.boys_height_2_18 = {
            'ages': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
            'p3': [81, 87, 93, 98, 103, 107, 111, 115, 119, 123, 127, 130, 133, 135, 137, 138, 139],
            'p10': [84, 90, 96, 101, 106, 110, 115, 119, 123, 127, 131, 134, 137, 139, 141, 142, 143],
            'p25': [88, 94, 99, 104, 109, 113, 118, 122, 126, 130, 134, 137, 140, 143, 145, 147, 148],
            'p50': [92, 98, 103, 108, 113, 117, 122, 126, 130, 134, 138, 141, 144, 147, 149, 151, 152],
            'p75': [96, 102, 107, 112, 117, 121, 126, 130, 134, 138, 142, 145, 148, 151, 153, 155, 156],
            'p90': [100, 106, 111, 116, 121, 125, 130, 134, 138, 142, 146, 149, 152, 155, 157, 159, 160],
            'p97': [104, 110, 115, 120, 125, 129, 134, 138, 142, 146, 150, 153, 156, 159, 161, 163, 164]
        }
        
        self.girls_height_2_18 = {
            'ages': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
            'p3': [80, 86, 92, 97, 102, 106, 110, 114, 118, 122, 126, 129, 132, 134, 136, 137, 138],
            'p10': [83, 89, 95, 100, 105, 109, 114, 118, 122, 126, 130, 133, 136, 138, 140, 141, 142],
            'p25': [87, 93, 98, 103, 108, 112, 117, 121, 125, 129, 133, 136, 139, 142, 144, 145, 146],
            'p50': [91, 97, 102, 107, 112, 116, 121, 125, 129, 133, 137, 140, 143, 146, 148, 149, 150],
            'p75': [95, 101, 106, 111, 116, 120, 125, 129, 133, 137, 141, 144, 147, 150, 152, 153, 154],
            'p90': [99, 105, 110, 115, 120, 124, 129, 133, 137, 141, 145, 148, 151, 154, 156, 157, 158],
            'p97': [103, 109, 114, 119, 124, 128, 133, 137, 141, 145, 149, 152, 155, 158, 160, 161, 162]
        }
        
        self.boys_weight_2_18 = {
            'ages': [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
            'p3': [10.5, 12.0, 13.5, 15.0, 16.5, 18.0, 20.0, 22.0, 24.5, 27.0, 30.0, 33.0, 37.0, 41.0, 46.0, 50.0, 54.0],
            'p10': [11.0, 12.5, 14.0, 15.5, 17.0, 19.0, 21.0, 23.5, 26.0, 28.5, 31.5, 35.0, 39.0, 43.5, 48.0, 52.5, 56.5],
            'p25': [11.8, 13.5, 15.0, 16.5, 18.0, 20.0, 22.5, 25.0, 28.0, 31.0, 34.0, 38.0, 42.0, 46.5, 51.0, 55.5, 59.5],
            'p50': [12.5, 14.2, 16.0, 17.5, 19.5, 21.5, 24.0, 27.0, 30.0, 33.5, 37.0, 41.0, 45.5, 50.0, 54.5, 59.0, 63.0],
            'p75': [13.5, 15.2, 17.0, 19.0, 21.0, 23.5, 26.0, 29.0, 32.5, 36.0, 40.0, 44.5, 49.0, 54.0, 58.5, 63.0, 67.0],
            'p90': [14.5, 16.5, 18.5, 20.5, 23.0, 25.5, 28.5, 32.0, 35.5, 39.5, 44.0, 48.5, 53.5, 58.5, 63.5, 68.0, 72.0],
            'p97': [15.8, 18.0, 20.0, 22.5, 25.0, 28.0, 31.5, 35.5, 39.5, 44.0, 49.0, 54.0, 59.5, 65.0, 70.0, 75.0, 79.0]
        }
    
    def convert_age_to_months(self, age_years):
        """将年龄转换为月份"""
        return age_years * 12
    
    def interpolate_percentiles(self, data_dict, target_age):
        """插值计算特定年龄的百分位数值"""
        ages = np.array(data_dict['ages'])
        percentiles = {}
        
        for key in data_dict:
            if key != 'ages':
                values = np.array(data_dict[key])
                f = interpolate.interp1d(ages, values, kind='linear', fill_value='extrapolate')
                percentiles[key] = float(f(target_age))
        
        return percentiles
    
    def determine_percentile_range(self, value, percentiles):
        """确定输入值位于哪个百分位数范围"""
        if value < percentiles['p3']:
            return "低于第3百分位数 (需要关注)"
        elif value < percentiles['p10']:
            return "第3-10百分位数 (偏低)"
        elif value < percentiles['p25']:
            return "第10-25百分位数 (正常偏低)"
        elif value < percentiles['p50']:
            return "第25-50百分位数 (正常)"
        elif value < percentiles['p75']:
            return "第50-75百分位数 (正常)"
        elif value < percentiles['p90']:
            return "第75-90百分位数 (正常偏高)"
        elif value < percentiles['p97']:
            return "第90-97百分位数 (偏高)"
        else:
            return "高于第97百分位数 (需要关注)"
    
    def plot_head_chart(self, gender, age, head_circumference):
        """绘制头围生长图表"""
        # 判断使用哪个年龄段的数据
        if age <= 2:
            age_months = age * 12  # 转换为月份
            if gender == "男":
                data = self.boys_head_0_2
                title = "男孩头围生长图表 (0-2岁)"
                age_unit = "月"
                target_age = age_months
            else:
                data = self.girls_head_0_2
                title = "女孩头围生长图表 (0-2岁)"
                age_unit = "月"
                target_age = age_months
        else:
            if gender == "男":
                data = self.boys_head_2_18
                title = "男孩头围生长图表 (2-18岁)"
                age_unit = "岁"
                target_age = age
            else:
                data = self.girls_head_2_18
                title = "女孩头围生长图表 (2-18岁)"
                age_unit = "岁"
                target_age = age
        
        ages = np.array(data['ages'])
        
        plt.figure(figsize=(12, 8))
        
        # 绘制百分位数曲线
        colors = ['red', 'orange', 'green', 'blue', 'green', 'orange', 'red']
        labels = ['第3百分位数', '第10百分位数', '第25百分位数', '第50百分位数', 
                 '第75百分位数', '第90百分位数', '第97百分位数']
        
        percentile_keys = ['p3', 'p10', 'p25', 'p50', 'p75', 'p90', 'p97']
        
        for i, key in enumerate(percentile_keys):
            plt.plot(ages, data[key], color=colors[i], label=labels[i], linewidth=2)
        
        # 标记宝宝的数据点
        plt.scatter(target_age, head_circumference, color='black', s=100, zorder=5, 
                   label=f'宝宝数据 ({age}岁, {head_circumference}cm)')
        
        # 计算宝宝所在的百分位数范围
        percentiles = self.interpolate_percentiles(data, target_age)
        percentile_range = self.determine_percentile_range(head_circumference, percentiles)
        
        plt.title(f'{title}\n宝宝位置: {percentile_range}', fontsize=14, fontweight='bold')
        plt.xlabel(f'年龄 ({age_unit})', fontsize=12)
        plt.ylabel('头围 (厘米)', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        # 输出详细分析
        print(f"\n=== 头围生长分析 ===")
        print(f"宝宝信息: {gender}孩, {age}岁, 头围{head_circumference}cm")
        print(f"百分位数位置: {percentile_range}")
        print(f"\n各百分位数参考值:")
        for key, label in zip(percentile_keys, labels):
            print(f"{label}: {percentiles[key]:.1f}cm")
        
        # 头围发育的临床意义说明
        print(f"\n=== 头围发育说明 ===")
        if head_circumference < percentiles['p3']:
            print("⚠️ 头围偏小需要关注，建议咨询儿科医生排除小头症等情况")
        elif head_circumference > percentiles['p97']:
            print("⚠️ 头围偏大需要关注，建议咨询儿科医生排除巨头症或其他疾病")
        else:
            print("✅ 头围在正常范围内，大脑发育良好")
    
    def plot_height_chart(self, gender, age, height, age_range="2-18"):
        """绘制身高生长图表"""
        if gender == "男" and age_range == "2-18":
            data = self.boys_height_2_18
            title = "男孩身高生长图表 (2-18岁)"
        elif gender == "女" and age_range == "2-18":
            data = self.girls_height_2_18
            title = "女孩身高生长图表 (2-18岁)"
        else:
            print("暂不支持该性别或年龄范围")
            return
        
        ages = np.array(data['ages'])
        
        plt.figure(figsize=(12, 8))
        
        colors = ['red', 'orange', 'green', 'blue', 'green', 'orange', 'red']
        labels = ['第3百分位数', '第10百分位数', '第25百分位数', '第50百分位数', 
                 '第75百分位数', '第90百分位数', '第97百分位数']
        
        percentile_keys = ['p3', 'p10', 'p25', 'p50', 'p75', 'p90', 'p97']
        
        for i, key in enumerate(percentile_keys):
            plt.plot(ages, data[key], color=colors[i], label=labels[i], linewidth=2)
        
        plt.scatter(age, height, color='black', s=100, zorder=5, label=f'宝宝数据 ({age}岁, {height}cm)')
        
        percentiles = self.interpolate_percentiles(data, age)
        percentile_range = self.determine_percentile_range(height, percentiles)
        
        plt.title(f'{title}\n宝宝位置: {percentile_range}', fontsize=14, fontweight='bold')
        plt.xlabel('年龄 (岁)', fontsize=12)
        plt.ylabel('身高 (厘米)', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        print(f"\n=== 身高生长分析 ===")
        print(f"宝宝信息: {gender}孩, {age}岁, 身高{height}cm")
        print(f"百分位数位置: {percentile_range}")
        print(f"\n各百分位数参考值:")
        for key, label in zip(percentile_keys, labels):
            print(f"{label}: {percentiles[key]:.1f}cm")
    
    def plot_weight_chart(self, gender, age, weight):
        """绘制体重生长图表"""
        if gender == "男":
            data = self.boys_weight_2_18
            title = "男孩体重生长图表 (2-18岁)"
        else:
            print("女孩体重数据暂未完全录入")
            return
        
        ages = np.array(data['ages'])
        
        plt.figure(figsize=(12, 8))
        
        colors = ['red', 'orange', 'green', 'blue', 'green', 'orange', 'red']
        labels = ['第3百分位数', '第10百分位数', '第25百分位数', '第50百分位数', 
                 '第75百分位数', '第90百分位数', '第97百分位数']
        
        percentile_keys = ['p3', 'p10', 'p25', 'p50', 'p75', 'p90', 'p97']
        
        for i, key in enumerate(percentile_keys):
            plt.plot(ages, data[key], color=colors[i], label=labels[i], linewidth=2)
        
        plt.scatter(age, weight, color='black', s=100, zorder=5, label=f'宝宝数据 ({age}岁, {weight}kg)')
        
        percentiles = self.interpolate_percentiles(data, age)
        percentile_range = self.determine_percentile_range(weight, percentiles)
        
        plt.title(f'{title}\n宝宝位置: {percentile_range}', fontsize=14, fontweight='bold')
        plt.xlabel('年龄 (岁)', fontsize=12)
        plt.ylabel('体重 (公斤)', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        print(f"\n=== 体重生长分析 ===")
        print(f"宝宝信息: {gender}孩, {age}岁, 体重{weight}kg")
        print(f"百分位数位置: {percentile_range}")


# 更新的主程序
def main():
    chart = GrowthChartHK()
    
    print("=== 香港儿童生长图表分析工具 ===")
    print("支持的分析指标：")
    print("1. 身高分析")
    print("2. 体重分析") 
    print("3. 头围分析 (新增)")
    
    # 示例：头围分析
    print("\n=== 示例：头围分析 ===")
    chart.plot_head_chart("男", 1.5, 47.0)  # 1.5岁男孩，头围47cm
    
    # 交互式输入
    print("\n=== 请输入您宝宝的信息 ===")
    try:
        gender = input("请输入性别 (男/女): ").strip()
        age = float(input("请输入年龄 (岁): "))
        
        print("\n请选择要分析的指标:")
        print("1 - 身高分析")
        print("2 - 体重分析") 
        print("3 - 头围分析")
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            height = float(input("请输入身高 (厘米): "))
            chart.plot_height_chart(gender, age, height)
        elif choice == "2":
            weight = float(input("请输入体重 (公斤): "))
            chart.plot_weight_chart(gender, age, weight)
        elif choice == "3":
            head_circumference = float(input("请输入头围 (厘米): "))
            chart.plot_head_chart(gender, age, head_circumference)
        else:
            print("选择无效")
            
    except ValueError:
        print("输入格式错误，请输入数字")
    except KeyboardInterrupt:
        print("\n程序已退出")

if __name__ == "__main__":
    main()
